# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/
.plugin_symlinks/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Web related

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release
.gradle/6.1.1/vcsMetadata-1/
.gradle/6.7/vcsMetadata-1/
app/src/main/java/io/flutter/plugins/
plugins/fl_chart-0.40.0/example/android/local.properties
plugins/fl_chart-0.40.0/example/android/app/src/main/java/io/flutter/plugins/GeneratedPluginRegistrant.java
launch.json
/pubspec.lock
/plugins/flutter_windows_bluetooth/windows/.vscode/
/plugins/flutter_windows_bluetooth/windows/.vs/

/windows/flutter/generated_plugin_registrant.cc
/windows/flutter/generated_plugins.cmake
/macos/Flutter/ephemeral/Flutter-Generated.xcconfig
/macos/Flutter/ephemeral/flutter_export_environment.sh
