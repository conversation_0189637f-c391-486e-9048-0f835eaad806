import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';

import '../db/database_helper.dart';
import '../db/entity/ptype.dart';
import '../db/model/ptype_model.dart';
import '../db/ptype_db_manager.dart';
import '../entity/page_info.dart';
import 'tool/sp_tool.dart';

typedef ProgressCallback = void Function(double progress, String message);

class PtypeSyncUtil {
  PtypeSyncUtil._();

  ///开始同步
  static Future<void> startSync(
      BuildContext context, ProgressCallback progressCallback) async {
    StringBuffer messageBuffer = StringBuffer("开始同步商品信息\n\n");
    progressCallback(0, messageBuffer.toString());
    int currentTimeStamp = DateTime.now().millisecondsSinceEpoch;
    Database db;
    try {
      db = await DatabaseHelper.instance.database;
    } catch (e) {
      messageBuffer.writeln("数据库加载失败\n${e.toString()}");
      progressCallback(1, messageBuffer.toString());
      rethrow;
    }

    messageBuffer.writeln("同步商品");
    await sync<PtypeDO>(
        messageBuffer: messageBuffer,
        callback: progressCallback,
        dataGetter: (pageIndex) async {
          if (!context.mounted) return null;
          final pageInfo =
              await PtypeModel.getPtype(context, pageIndex: pageIndex);
          if (pageIndex == 1) {
            await PtypeDbManager.createPtypeTable(db, currentTimeStamp);
          }
          if (pageInfo.list.isNotEmpty) {
            await PtypeDbManager.insertPtype(
                pageInfo.list
                    .map((e) =>
                        DatabaseHelper.changeCamelCaseMapToUnderscoreCaseMap(
                            e.toJson()))
                    .toList(growable: false),
                currentTimeStamp);
          }
          return pageInfo;
        });

    messageBuffer.writeln("同步商品价格");
    await sync<PriceDO>(
        messageBuffer: messageBuffer,
        callback: progressCallback,
        dataGetter: (pageIndex) async {
          if (!context.mounted) return null;
          final pageInfo = await PtypeModel.getPtypePrice(context,
              pageIndex: pageIndex, type: 0);
          if (pageIndex == 1) {
            await PtypeDbManager.createPtypePriceTable(db, currentTimeStamp);
          }
          if (pageInfo.list.isNotEmpty) {
            await PtypeDbManager.insertPtypePrice(
                pageInfo.list
                    .map((e) =>
                        DatabaseHelper.changeCamelCaseMapToUnderscoreCaseMap(
                            e.toPtypePriceJson()))
                    .toList(growable: false),
                currentTimeStamp);
          }
          return pageInfo;
        });

    messageBuffer.writeln("同步门店价格本");
    await sync<PriceDO>(
        messageBuffer: messageBuffer,
        callback: progressCallback,
        dataGetter: (pageIndex) async {
          if (!context.mounted) return null;
          final pageInfo = await PtypeModel.getPtypePrice(context,
              pageIndex: pageIndex, type: 1);
          if (pageIndex == 1) {
            await PtypeDbManager.createOtypePriceTable(db, currentTimeStamp);
          }
          if (pageInfo.list.isNotEmpty) {
            await PtypeDbManager.insertOtypePrice(
                pageInfo.list
                    .map((e) =>
                        DatabaseHelper.changeCamelCaseMapToUnderscoreCaseMap(
                            e.toOtypePriceJson()))
                    .toList(growable: false),
                currentTimeStamp);
          }
          return pageInfo;
        });

    messageBuffer.writeln("同步商品权限");
    await sync<PtypeLimitDO>(
        messageBuffer: messageBuffer,
        callback: progressCallback,
        dataGetter: (pageIndex) async {
          if (!context.mounted) return null;
          final pageInfo =
              await PtypeModel.getPtypeLimit(context, pageIndex: pageIndex);
          if (pageIndex == 1) {
            await PtypeDbManager.createPtypeLimitTable(db, currentTimeStamp);
          }
          if (pageInfo.list.isNotEmpty) {
            await PtypeDbManager.insertPtypeLimit(
                pageInfo.list
                    .map((e) =>
                        DatabaseHelper.changeCamelCaseMapToUnderscoreCaseMap(
                            e.toJson()))
                    .toList(growable: false),
                currentTimeStamp);
          }
          return pageInfo;
        });

    messageBuffer.writeln("同步商品库存");
    await sync<StockDO>(
        messageBuffer: messageBuffer,
        callback: progressCallback,
        dataGetter: (pageIndex) async {
          if (!context.mounted) return null;
          final pageInfo =
              await PtypeModel.getPtypeStock(context, pageIndex: pageIndex);
          if (pageIndex == 1) {
            await PtypeDbManager.createPtypeStockTable(db, currentTimeStamp);
          }
          if (pageInfo.list.isNotEmpty) {
            await PtypeDbManager.insertPtypeStock(
                pageInfo.list
                    .map((e) =>
                        DatabaseHelper.changeCamelCaseMapToUnderscoreCaseMap(
                            e.toJson()))
                    .toList(growable: false),
                currentTimeStamp);
          }
          return pageInfo;
        });

    messageBuffer.writeln("同步商品SKU");
    await sync<SkuDO>(
        messageBuffer: messageBuffer,
        callback: progressCallback,
        dataGetter: (pageIndex) async {
          if (!context.mounted) return null;
          final pageInfo =
              await PtypeModel.getPtypeSku(context, pageIndex: pageIndex);
          if (pageIndex == 1) {
            await PtypeDbManager.createPtypeSkuTable(db, currentTimeStamp);
          }
          if (pageInfo.list.isNotEmpty) {
            await PtypeDbManager.insertPtypeSku(
                pageInfo.list
                    .map((e) =>
                        DatabaseHelper.changeCamelCaseMapToUnderscoreCaseMap(
                            e.toJson()))
                    .toList(growable: false),
                currentTimeStamp);
          }
          return pageInfo;
        });

    messageBuffer.writeln("同步商品单位");
    await sync<UnitDO>(
        messageBuffer: messageBuffer,
        callback: progressCallback,
        dataGetter: (pageIndex) async {
          if (!context.mounted) return null;

          final pageInfo =
              await PtypeModel.getPtypeUnit(context, pageIndex: pageIndex);
          if (pageIndex == 1) {
            await PtypeDbManager.createPtypeUnitTable(db, currentTimeStamp);
          }
          if (pageInfo.list.isNotEmpty) {
            await PtypeDbManager.insertPtypeUnit(
                pageInfo.list
                    .map((e) =>
                        DatabaseHelper.changeCamelCaseMapToUnderscoreCaseMap(
                            e.toJson()))
                    .toList(growable: false),
                currentTimeStamp);
          }
          return pageInfo;
        });

    messageBuffer.writeln("同步套餐");
    await sync<ComboDetailDO>(
        messageBuffer: messageBuffer,
        callback: progressCallback,
        dataGetter: (pageIndex) async {
          if (!context.mounted) return null;
          final pageInfo = await PtypeModel.getPtypeComboDetail(context,
              pageIndex: pageIndex);
          if (pageIndex == 1) {
            await PtypeDbManager.createComboDetailTable(db, currentTimeStamp);
          }
          if (pageInfo.list.isNotEmpty) {
            await PtypeDbManager.insertComboDetail(
                pageInfo.list
                    .map((e) =>
                        DatabaseHelper.changeCamelCaseMapToUnderscoreCaseMap(
                            e.toJson()))
                    .toList(growable: false),
                currentTimeStamp);
          }
          return pageInfo;
        });

    messageBuffer.writeln("同步商品条码");
    await sync<FullBarcodeDO>(
        messageBuffer: messageBuffer,
        callback: progressCallback,
        dataGetter: (pageIndex) async {
          if (!context.mounted) return null;
          final pageInfo =
              await PtypeModel.getFullBarcode(context, pageIndex: pageIndex);
          if (pageIndex == 1) {
            await PtypeDbManager.createFullBarcodeTable(db, currentTimeStamp);
          }
          if (pageInfo.list.isNotEmpty) {
            await PtypeDbManager.insertFullBarcode(
                pageInfo.list
                    .map((e) =>
                        DatabaseHelper.changeCamelCaseMapToUnderscoreCaseMap(
                            e.toJson()))
                    .toList(growable: false),
                currentTimeStamp);
          }
          return pageInfo;
        });
    messageBuffer.writeln("同步商品编码");
    await sync<XcodeDO>(
        messageBuffer: messageBuffer,
        callback: progressCallback,
        dataGetter: (pageIndex) async {
          if (!context.mounted) return null;
          final pageInfo =
              await PtypeModel.getPtypeXcode(context, pageIndex: pageIndex);
          if (pageIndex == 1) {
            await PtypeDbManager.createXcodeTable(db, currentTimeStamp);
          }
          if (pageInfo.list.isNotEmpty) {
            await PtypeDbManager.insertXcode(
                pageInfo.list
                    .map((e) =>
                        DatabaseHelper.changeCamelCaseMapToUnderscoreCaseMap(
                            e.toJson()))
                    .toList(growable: false),
                currentTimeStamp);
          }
          return pageInfo;
        });
    if (context.mounted) {
      final dbConfig = SpTool.getDatabaseConfig();
      dbConfig.ptypeTimestamp = currentTimeStamp;
      dbConfig.hasPtypeData = true;
      SpTool.saveDatabaseConfig(dbConfig);
      messageBuffer.writeln("删除过期数据");
      progressCallback(0, messageBuffer.toString());
      await PtypeDbManager.dropPtypeTables(
          db: db, excludeTimeStamp: currentTimeStamp);
      messageBuffer.writeln("商品信息同步完成 \n\n");

      progressCallback(1, messageBuffer.toString());
    }
  }

  ///同步各类商品信息，处理进度
  static Future<void> sync<T>({
    required Future<PageInfo<T>?> Function(int pageIndex) dataGetter,
    StringBuffer? messageBuffer,
    ProgressCallback? callback,
  }) async {
    messageBuffer?.writeln("开始同步");
    callback?.call(0, messageBuffer?.toString() ?? "");
    int pageIndex = 1;
    PageInfo? pageInfo;
    do {
      pageInfo = await dataGetter(pageIndex++);
      if (pageInfo != null && pageInfo.hasNextPage) {
        if (callback != null) {
          final progress = Decimal.fromInt(pageInfo.pageNum) /
              Decimal.fromInt(pageInfo.pages);
          callback.call(
              progress
                  .toDecimal(scaleOnInfinitePrecision: 8)
                  .round(scale: 2)
                  .toDouble(),
              messageBuffer?.toString() ?? "");
        }
      }
    } while (pageInfo?.hasNextPage == true);
    if (pageInfo != null) {
      messageBuffer?.writeln("同步完成\n");
      callback?.call(1, messageBuffer?.toString() ?? "");
    }
  }
}
