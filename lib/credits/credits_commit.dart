import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:halo_pos/bill/tool/goods_tool.dart';
import '../../../credits/entity/promotion_credits_dto.dart';
import '../../../widgets/halo_pos_label.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart';

import '../bill/entity/goods_detail_dto.dart';
import '../bill/entity/ptype/ptype_serial_no_dto.dart';
import '../bill/tool/scan_tool.dart';
import '../bill/widget/ptype/goods_bind_batch_page.dart';
import '../bill/widget/ptype/goods_bind_sn_page.dart';
import '../common/tool/sp_tool.dart';

class CreditsCommit extends StatefulWidget {
  final VoidCallback? onCommit;
  final VoidCallback? onCancel;
  final PromotionCreditsDTO promotionCreditsDTO;

  const CreditsCommit(
      {this.onCommit, required this.promotionCreditsDTO, this.onCancel})
      : super();

  @override
  State<CreditsCommit> createState() => _CreditsCommitState();
}

class _CreditsCommitState extends State<CreditsCommit> {
  final double width = ScreenUtil().screenWidth - 800.w;
  final double height = ScreenUtil().screenHeight - 500.w;

  final TextEditingController controllerSn = TextEditingController();
  final TextEditingController controllerBatch = TextEditingController();

  late GoodsDetailDto detailDto;

  @override
  initState() {
    super.initState();
    initData();
  }

  initData () async {
    if (widget.promotionCreditsDTO.ptypeGroup == 3) {
      ///不做套餐的批次
      var storeInfo = SpTool.getStoreInfo()!;
      List<GoodsDetailDto>details = [];

      GoodsDetailDto dto = GoodsDetailDto()
        ..kfullname = storeInfo.ktypeName
        ..ktypeId = storeInfo.ktypeId
        ..pFullName = widget.promotionCreditsDTO.creditsName ?? ""
        ..ptypeId = widget.promotionCreditsDTO.ptypeId ?? "0"
        ..skuId = widget.promotionCreditsDTO.skuId ?? "0"
        ..unitId = widget.promotionCreditsDTO.unitId ?? "0"
        ..unitRate = num.tryParse(widget.promotionCreditsDTO.unitRate ?? "1") ?? 1
        ..unitName = widget.promotionCreditsDTO.unitName ?? ""
        ..unitQty = 1
        ..stockQty = 1
        ..fullbarcode = widget.promotionCreditsDTO.fullbarcode ?? ""
        ..batchenabled = widget.promotionCreditsDTO.batchenabled == 1
        ..picUrl = widget.promotionCreditsDTO.picUrl
        ..costMode = widget.promotionCreditsDTO.costMode ?? 0
        ..protectDays = widget.promotionCreditsDTO.protectDays??0
        ..snenabled = widget.promotionCreditsDTO.snenabled ?? 0;
      details.add(dto);
      await ScanTool.getPtypeAutoBatch(details, context);
      detailDto = details.first;
      widget.promotionCreditsDTO.batchNo = details.first.batchNo;
      widget.promotionCreditsDTO.produceDate = details.first.produceDate;
      widget.promotionCreditsDTO.expireDate = details.first.expireDate;
      widget.promotionCreditsDTO.costId = details.first.costId;
      widget.promotionCreditsDTO.batchPrice = details.first.batchPrice;
      widget.promotionCreditsDTO.pUserCode = details.first.pUserCode;
      setState(() {
        final double height = ScreenUtil().screenHeight - 500.w;
        controllerBatch.text = widget.promotionCreditsDTO.batchNo ?? "";
      });
    }
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Color.fromRGBO(0, 0, 0, 0),
        body: Container(
          alignment: Alignment.topCenter,
          margin: EdgeInsets.only(
              left: (ScreenUtil().screenWidth - 580.w) / 2,
              right: (ScreenUtil().screenWidth - 580.w) / 2,
              top: (ScreenUtil().screenHeight - getHeight(context)) / 2,
              bottom: (ScreenUtil().screenHeight - getHeight(context)) / 2),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.w), color: Colors.white),
          child: ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: double.infinity,
              ),
              child: HaloContainer(
                direction: Axis.vertical,
                crossAxisAlignment: CrossAxisAlignment.start,
                padding: EdgeInsets.all(35.w),
                children: [
                  Container(
                    height: 350.w,
                    child: PromotionCreditsDTO.getPic(
                        ptypeGroup: widget.promotionCreditsDTO.ptypeGroup,
                        picUrl: widget.promotionCreditsDTO.picUrl ?? "",
                        valueType: int.tryParse(
                            widget.promotionCreditsDTO.valueType ?? "")),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: 10.w),
                    child: HaloPosLabel(
                      widget.promotionCreditsDTO.preferential! + "积分",
                      textStyle: TextStyle(
                          color: ColorUtil.stringColor("#FF4141"),
                          fontSize: 30.sp,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.only(top: 10.w),
                      child: HaloPosLabel(
                        widget.promotionCreditsDTO.ptypeGroup! == 7
                            ? "储值金额：${widget.promotionCreditsDTO.price}元"
                            : widget.promotionCreditsDTO.getName()!,
                        maxLines: 2,
                        textStyle: TextStyle(
                            color: ColorUtil.stringColor("#333333"),
                            fontSize: 28.sp,
                            fontWeight: FontWeight.w600),
                      ),
                    ),
                  ),
                  Visibility(visible: widget.promotionCreditsDTO.snenabled == 1|| widget.promotionCreditsDTO.snenabled == 2, child: _buildEditContent(context, "序列号：", controllerSn, () {
                    _btnSn(detailDto);
                  })),
                  Visibility(visible: widget.promotionCreditsDTO.batchenabled==1, child: _buildEditContent(context, "批次号：", controllerBatch, () {
                    _btnBatch(detailDto);
                  })),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildButton("取消", HaloButtonType.outlinedButton,
                          borderColor: Color(0xFFB7B7B7), callback: () {
                        Navigator.pop(context);
                        widget.onCancel!();
                      }),
                      _buildButton("确认兑换", HaloButtonType.elevatedButton,
                          borderColor: Color(0xFFB7B7B7),
                          callback: widget.onCommit!)
                    ],
                  ),
                ],
              )),
        ));
  }

  getHeight(BuildContext context) {
    double height = 650.w;
   if(widget.promotionCreditsDTO.batchenabled == 1){
     height+=50;
   }
    if(widget.promotionCreditsDTO.snenabled == 1|| widget.promotionCreditsDTO.snenabled == 2){
      height+=50;
    }
    return height;
  }

  Widget _buildEditContent(BuildContext context,String text,TextEditingController controller,Function() tap){
    return GestureDetector(
      onTap: tap,
      child: HaloContainer(
        margin: EdgeInsets.only(left: 0.w,right: 0.w),
        children: [
          HaloPosLabel(text),
          Flexible(
            child: TextField(
              enabled: false,
              decoration: const InputDecoration(
                border: InputBorder.none,
                counterText: "",
              ),
              controller: controller,
              textAlign: TextAlign.start,
              style: TextStyle(fontSize: 24.sp),
              maxLines: 1,
              onChanged: (text){

              },
            ),
          ),
          Image.asset('assets/images/edit.png')
        ],
      ),
    );
  }

  String getSnStr(List<PtypeSerialNoDto>snList){
    return snList.map((e) => e.snno).toList().join(",");
  }

  ///批次号
  _btnBatch(GoodsDetailDto goods){
    showDialog(
        context: context,
        builder: (context) => GoodsBindBatchPage(goods: goods)).then((value) {
      setState(() {
        controllerBatch.text =goods.batchNo;
        widget.promotionCreditsDTO.batchNo = goods.batchNo;
        widget.promotionCreditsDTO.produceDate = goods.produceDate;
        widget.promotionCreditsDTO.expireDate = goods.expireDate;
        widget.promotionCreditsDTO.batchPrice = goods.batchPrice;
        widget.promotionCreditsDTO.costId = goods.costId;
        widget.promotionCreditsDTO.batchPrice = goods.batchPrice;
        widget.promotionCreditsDTO.pUserCode = goods.pUserCode;
      });
    });
  }

  ///序列号选择
  _btnSn(GoodsDetailDto goods) {
    //如果是批次号商品，且没有选择批次号，那么需要先选择批次号
    if (goods.batchenabled && !GoodsTool.isGoodsBindWithBatch(goods)) {
      // showDialog(
      //     context: context,
      //     builder: (context) => GoodsBindBatchPage(goods: goods));
      HaloToast.showInfo(context,msg: "请先选择批次号");
      return;
    }
    //点击跳转到选择商品序列号弹窗
    showDialog(
        context: context,
        builder: (context) => GoodsBindSnPage(
          goods: goods,
          existSN: [
            ...goods.serialNoList.map((e) => e.snno??"")
          ],
          limitMaxCount: true,
        )).then((value) {
      if (value != null) {
        controllerSn.text = getSnStr(goods.serialNoList);
        widget.promotionCreditsDTO.serialNoList = goods.serialNoList;
        setState(() {});
      }
    });
  }

  _buildButton(String text, HaloButtonType type,
      {required Color borderColor, required VoidCallback callback}) {
    return HaloButton(
      width: 240.w,
      height: 66.w,
      fontSize: 26.w,
      buttonType: type,
      borderRadius: 4.w,
      text: text,
      borderColor: borderColor,
      outLineWidth: 2.w,
      onPressed: () {
        callback();
      },
    );
  }
}
