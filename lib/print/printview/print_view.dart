// import 'package:flutter/gestures.dart';
// import 'package:flutter/material.dart';
// import 'package:halo_utils/halo_utils.dart';
// import 'package:halo_utils/navigator/navigate_util.dart';
// import 'package:halo_utils/utils/string_util.dart';
// import 'package:haloui/haloui.dart';
// import 'package:haloui/widget/halo_radio.dart';
// import 'package:qr_flutter/qr_flutter.dart';
//
// import '../../../common/login/login_center.dart';
// import '../../../common/string_res.dart';
// import '../../../common/style/app_color_helper.dart';
// import '../../../common/style/app_colors.dart';
// import '../../../common/tool/dialog_util.dart';
// import '../../../common/tool/sp_tool.dart';
// import '../../../enum/bill_type.dart';
// import '../../../enum/setting/print_width_type.dart';
// import '../../../iconfont/icon_font.dart';
// import '../../../print/tool/print_tool.dart';
// import '../../../settting/widget/system_config_page.dart';
// import '../../../widgets/base/base_stateful_page.dart';
// import '../../../widgets/custom_table.dart';
// import '../../../widgets/dotted_line.dart';
// import '../../../widgets/halo_pos_label.dart';
// import '../../login/model/login_user_model.dart';
//
// ///
// ///@ClassName: print_view
// ///@Description: 打印预览
// ///@Author: tanglan
// ///@Date: 8/18/21 1:18 PM
// @Deprecated("使用新配置模板替换")
// class PrintView extends BaseStatefulPage {
//   const PrintView({Key? key}) : super(key: key, rightFlex: 0);
//
//   @override
//   BaseStatefulPageState<PrintView> createState() {
//     return _PrintViewState();
//   }
// }
//
// class _PrintViewState extends BaseStatefulPageState<PrintView> {
//   ///选择打印尺寸，根据打印配置获取，默认为58mm
//   PrintWidth selectPrintSize =
//       PrintWidth.values[SpTool.getSetting().printPaperWidth];
//
//   ///单据选中index，和billTypeList配合使用
//   int selectIndex = 0;
//
//   ///支持配置打印样式的单据
//   final List<Map<String, String>> billTypeList = [
//     {"title": "销售出库单", "billType": BillTypeData[BillType.SaleBill]!},
//     {"title": "销售退货单", "billType": BillTypeData[BillType.SaleBackBill]!},
//     {"title": "调拨单", "billType": BillTypeData[BillType.GoodsTrans]!},
//     {"title": "调拨订单", "billType": BillTypeData[BillType.TransferOrder]!},
//     {"title": "全渠道订单", "billType": BillTypeData[BillType.ChannelBill]!}
//   ];
//
//   ///获取对应单据的打印配置项
//   List<String>? selectPrintConfigName =
//       PrintFieldConfig.printConfigName[BillTypeData[BillType.SaleBill]];
//
//   ///获取对应单据的已选配置项
//   Map<String, dynamic> selectPrintConfig =
//       PrintTool.getPrintConfig(BillTypeData[BillType.SaleBill] ?? "");
//
//   @override
//   Widget buildLeftBody(BuildContext context) {
//     return HaloContainer(
//       direction: Axis.vertical,
//       mainAxisSize: MainAxisSize.max,
//       mainAxisAlignment: MainAxisAlignment.start,
//       children: [
//         // Expanded(
//         //     child: HaloContainer(
//         //         crossAxisAlignment: CrossAxisAlignment.start,
//         //         children: [
//         //       Expanded(
//         //           flex: 2,
//         //           child: HaloContainer(
//         //             color: Colors.white,
//         //             crossAxisAlignment: CrossAxisAlignment.start,
//         //             padding: EdgeInsets.only(left: 24.w, top: 16.w),
//         //             border: const Border(
//         //                 right: BorderSide(color: AppColors.borderColor)),
//         //             direction: Axis.vertical,
//         //             children: [
//         //               Text(
//         //                 "选择单据",
//         //                 style: TextStyle(
//         //                     color:
//         //                         AppColorHelper(context).getTitleBoldTextColor(),
//         //                     fontSize: 28.sp),
//         //               ),
//         //               SizedBox(height: 21.w),
//         //               Expanded(
//         //                   child: ListView.builder(
//         //                       itemCount: billTypeList.length,
//         //                       itemBuilder: (item, index) {
//         //                         return buildBillTypeItem(
//         //                             billTypeList[index]["title"], index);
//         //                       }))
//         //             ],
//         //           )),
//         //       Expanded(
//         //           flex: 2,
//         //           child: HaloContainer(
//         //               color: Colors.white,
//         //               crossAxisAlignment: CrossAxisAlignment.start,
//         //               padding:
//         //                   EdgeInsets.only(left: 24.w, right: 24.w, top: 16.w),
//         //               direction: Axis.vertical,
//         //               children: [
//         //                 Text(
//         //                   "设置打印字段",
//         //                   style: TextStyle(
//         //                       color: AppColorHelper(context)
//         //                           .getTitleBoldTextColor(),
//         //                       fontSize: 28.sp),
//         //                 ),
//         //                 SizedBox(height: 21.w),
//         //                 Expanded(
//         //                     child: ListView.builder(
//         //                         itemCount: selectPrintConfigName!.length,
//         //                         itemBuilder: (buildContext, index) {
//         //                           String key = selectPrintConfigName![index];
//         //                           return _buildSettingItem(key,
//         //                               enable: !PrintFieldConfig
//         //                                   .unCancelPrintConfig
//         //                                   .contains(key));
//         //                         }))
//         //               ])),
//         //       Container(
//         //         padding: EdgeInsets.only(left: 100.w, right: 24.w, top: 16.w),
//         //         child: Text(
//         //           "预览",
//         //           style: TextStyle(
//         //               color: AppColorHelper(context).getTitleBoldTextColor(),
//         //               fontSize: 28.sp),
//         //         ),
//         //       ),
//         //       Expanded(
//         //           flex: 5,
//         //           child: HaloContainer(
//         //             direction: Axis.horizontal,
//         //             margin: EdgeInsets.all(24.w),
//         //             crossAxisAlignment: CrossAxisAlignment.end,
//         //             children: [
//         //               Expanded(
//         //                   child: HaloContainer(
//         //                 direction: Axis.vertical,
//         //                 width: selectPrintSize == PrintWidth.w_58mm
//         //                     ? 480.w
//         //                     : 500.w,
//         //                 children: [
//         //                   HaloContainer(
//         //                     border: Border.all(
//         //                         color: AppColorHelper(context).getAccentColor(),
//         //                         width: 1),
//         //                     borderRadius:
//         //                         const BorderRadius.all(Radius.circular(4)),
//         //                     children: [
//         //                       GestureDetector(
//         //                           onTap: () {
//         //                             setState(() {
//         //                               selectPrintSize = PrintWidth.w_58mm;
//         //                             });
//         //                           },
//         //                           child: Container(
//         //                               color:
//         //                                   selectPrintSize == PrintWidth.w_58mm
//         //                                       ? AppColorHelper(context)
//         //                                           .getAccentColor()
//         //                                       : Colors.white,
//         //                               alignment: Alignment.center,
//         //                               width: 128.w,
//         //                               height: 40.w,
//         //                               child: Text(
//         //                                 PrintWidthExtension(PrintWidth.w_58mm)
//         //                                     .name,
//         //                                 style: TextStyle(
//         //                                     fontSize: 22.sp,
//         //                                     color: selectPrintSize ==
//         //                                             PrintWidth.w_58mm
//         //                                         ? Colors.white
//         //                                         : AppColorHelper(context)
//         //                                             .getAccentColor()),
//         //                               ))),
//         //                       GestureDetector(
//         //                           onTap: () {
//         //                             setState(() {
//         //                               selectPrintSize = PrintWidth.w_80mm;
//         //                             });
//         //                           },
//         //                           child: Container(
//         //                               alignment: Alignment.center,
//         //                               width: 128.w,
//         //                               height: 40.w,
//         //                               color:
//         //                                   selectPrintSize == PrintWidth.w_80mm
//         //                                       ? AppColorHelper(context)
//         //                                           .getAccentColor()
//         //                                       : Colors.white,
//         //                               child: Text(
//         //                                 PrintWidthExtension(PrintWidth.w_80mm)
//         //                                     .name,
//         //                                 style: TextStyle(
//         //                                     fontSize: 22.sp,
//         //                                     color: selectPrintSize ==
//         //                                             PrintWidth.w_80mm
//         //                                         ? Colors.white
//         //                                         : AppColorHelper(context)
//         //                                             .getAccentColor()),
//         //                               ))),
//         //                     ],
//         //                   ),
//         //                   Expanded(
//         //                       child: Container(
//         //                           width: selectPrintSize == PrintWidth.w_58mm
//         //                               ? 420.w
//         //                               : 440.w,
//         //                           margin: EdgeInsets.only(
//         //                             top: 22.w,
//         //                           ),
//         //                           decoration: const BoxDecoration(
//         //                               image: DecorationImage(
//         //                                   fit: BoxFit.cover,
//         //                                   image: AssetImage(
//         //                                     "assets/images/xiaopiao.png",
//         //                                   ))),
//         //                           padding: EdgeInsets.symmetric(
//         //                               vertical: 42.w, horizontal: 30.w),
//         //                           child: MediaQuery.removePadding(
//         //                               context: context,
//         //                               removeTop: true,
//         //                               removeBottom: true,
//         //                               child: ListView(
//         //                                 children: [
//         //                                   _buildPrintTitle(),
//         //                                   (billTypeList[selectIndex]
//         //                                                   ["billType"] ==
//         //                                               BillTypeData[BillType
//         //                                                   .GoodsTrans] ||
//         //                                           billTypeList[selectIndex]
//         //                                                   ["billType"] ==
//         //                                               BillTypeData[BillType
//         //                                                   .TransferOrder])
//         //                                       ? _buildTenderPrintContent()
//         //                                       : billTypeList[selectIndex]
//         //                                                   ["billType"] ==
//         //                                               BillTypeData[
//         //                                                   BillType.ChannelBill]
//         //                                           ? _buildChannelPrintContent()
//         //                                           : _buildPrintContent(),
//         //                                   _buildPrintFooter()
//         //                                 ],
//         //                               )))),
//         //                 ],
//         //               )),
//         //               RichText(
//         //                 text: TextSpan(
//         //                     text: "小票纸张宽度",
//         //                     style: TextStyle(
//         //                         fontSize: 24.w,
//         //                         color: AppColorHelper(context)
//         //                             .getTitleBoldTextColor()),
//         //                     children: [
//         //                       TextSpan(
//         //                           text: "点击此前往配置",
//         //                           style: TextStyle(
//         //                               fontSize: 24.w,
//         //                               color: AppColorHelper(context)
//         //                                   .getAccentColor()),
//         //                           recognizer: TapGestureRecognizer()
//         //                             ..onTap = () {
//         //                               NavigateUtil.navigateTo(
//         //                                   context, const SystemConfigPage());
//         //                             }),
//         //                     ]),
//         //               )
//         //             ],
//         //           )),
//         //     ]))
//       ],
//     );
//   }
//
//   //region 选择单据
//
//   ///构建单据选择列表
//   Widget buildBillTypeItem(String? key, index) {
//     return GestureDetector(
//         onTap: () {
//           if (checkUnSaveTips()) {
//             DialogUtil.showConfirmDialog(context,
//                 content: StringRes.TIP_PRINT_CHANGE_BILL.getText(context),
//                 actionLabels: ["取消", "确定"], confirmCallback: () {
//               changeBill(index);
//               return;
//             });
//             return;
//           }
//           changeBill(index);
//         },
//         child: Container(
//           height: 80.w,
//           alignment: Alignment.center,
//           decoration: BoxDecoration(
//               color:
//                   selectIndex == index ? AppColors.dividerColor : Colors.white,
//               border: Border.all(color: AppColors.dividerColor)),
//           child: Text(key ?? ""),
//         ));
//   }
//
//   ///单据切换
//   void changeBill(int index) {
//     setState(() {
//       selectIndex = index;
//       selectPrintConfigName = PrintFieldConfig
//           .printConfigName[billTypeList[selectIndex]["billType"]];
//       selectPrintConfig =
//           PrintTool.getPrintConfig(billTypeList[selectIndex]["billType"] ?? "");
//     });
//   }
//
//   ///单据切换未保存提示
//   bool checkUnSaveTips() {
//     Map<String, dynamic> oldSelectPrintConfig =
//         PrintTool.getPrintConfig(billTypeList[selectIndex]["billType"] ?? "");
//
//     for (String key in selectPrintConfig.keys) {
//       if (!oldSelectPrintConfig.containsKey(key) ||
//           oldSelectPrintConfig[key] != selectPrintConfig[key]) {
//         return true;
//       }
//     }
//     return false;
//   }
//
//   //endregion
//
//   //region  设置打印字段
//   ///构建的打印字段行
//   // Widget _buildSettingItem(String fieldKey, {bool enable = true}) {
//   //   if (PrintFieldConfig.subLevelPrintConfig.contains(fieldKey)) {
//   //     return Container();
//   //   }
//   //   return HaloContainer(
//   //     direction: Axis.vertical,
//   //     borderRadius: BorderRadius.all(Radius.circular(6.w)),
//   //     margin: EdgeInsets.only(bottom: 14.w),
//   //     padding:
//   //         EdgeInsets.only(left: 28.w, right: 30.w, top: 16.w, bottom: 16.w),
//   //     border: Border.all(color: AppColors.dividerColor),
//   //     children: getPrintItemRow(fieldKey, enable),
//   //   );
//   // }
//
//   List<Widget> getPrintItemRow(String fieldKey, enable) {
//     List<Widget> widgetList = [];
//     widgetList.add(buildItemRowWidget(fieldKey, enable));
//
//     // ///不存在树形子项，打印字段项构建结束
//     // if (!PrintFieldConfig.treePrintConfig.containsKey(fieldKey)) {
//     //   return widgetList;
//     // }
//     //
//     // List<String> subFieldList =
//     //     PrintFieldConfig.treePrintConfig[fieldKey] ?? [];
//     //
//     // ///树形子项未配置，打印字段项构建结束
//     // if (subFieldList.isEmpty) {
//     //   return widgetList;
//     // }
//     //
//     // ///商品编码中的树形子项单选，特殊处理
//     // if (fieldKey == PrintFieldConfig.pTypeCode) {
//     //   widgetList.addAll(buildPTypeCodeRowWidget());
//     //   return widgetList;
//     // }
//     //
//     // ///构建树形叶子组件
//     // for (String subKey in subFieldList) {
//     //   widgetList.add(buildItemRowWidget(subKey, enable, isSubLevel: true));
//     // }
//
//     return widgetList;
//   }
//
//   ///构建的打印配置项内容
//   Widget buildItemRowWidget(String fieldKey, enable,
//       {bool isSubLevel = false}) {
//     String printConfigName =
//         PrintFieldConfig.printConfigNameStr[fieldKey] ?? "";
//
//     ///树形子级需要增加空格
//     if (isSubLevel) {
//       printConfigName = "    $printConfigName";
//     }
//     return HaloContainer(
//       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       mainAxisSize: MainAxisSize.max,
//       children: [
//         HaloPosLabel(
//           printConfigName,
//           textStyle: TextStyle(
//               color: AppColorHelper(context).getTitleBoldTextColor(),
//               fontSize: 24.sp),
//         ),
//         GestureDetector(
//           onTap: !enable
//               ? null
//               : () {
//                   selectPrintConfig[fieldKey] =
//                       !(selectPrintConfig[fieldKey] ?? false);
//                   setState(() {});
//                 },
//           child: IconFont(
//             !enable
//                 ? IconNames.bunengbianji
//                 : (selectPrintConfig[fieldKey] ?? false
//                     ? IconNames.xuanzhong
//                     : IconNames.weixuanzhong),
//             size: 26.w,
//           ),
//         )
//       ],
//     );
//   }
//
//   ///构建商品编码树形子项
//   // List<Widget> buildPTypeCodeRowWidget() {
//   // if (!selectPrintConfig.containsKey(PrintFieldConfig.pTypeCode) ||
//   //     selectPrintConfig[PrintFieldConfig.pTypeCode] == false) {
//   //   return [];
//   // }
//   //
//   // List<String> subFieldList =
//   //     PrintFieldConfig.treePrintConfig[PrintFieldConfig.pTypeCode] ?? [];
//   //
//   // ///树形子级需要增加空格
//   // String groupValue = PrintFieldConfig.pTypUserCode;
//   //
//   // ///已选配置中包含编码配置，且未ture，radio的groupvalue则为该选项
//   // for (var element in subFieldList) {
//   //   if (selectPrintConfig.containsKey(element) &&
//   //       selectPrintConfig[element] == true) {
//   //     groupValue = element;
//   //   }
//   // }
//   //
//   // ///构建单选的打印配置
//   // List<Widget> subCodeList = [];
//   // for (String fieldKey in subFieldList) {
//   //   subCodeList.add(HaloContainer(
//   //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//   //     mainAxisSize: MainAxisSize.max,
//   //     children: [
//   //       HaloPosLabel(
//   //         "   ${PrintFieldConfig.printConfigNameStr[fieldKey]} ",
//   //         textStyle: TextStyle(
//   //             color: AppColorHelper(context).getTitleBoldTextColor(),
//   //             fontSize: 24.sp),
//   //       ),
//   //       HaloRadio(
//   //         value: fieldKey,
//   //         enabled: selectPrintConfig[PrintFieldConfig.pTypeCode] ?? false,
//   //         groupValue: groupValue,
//   //         imageSize: 30.w,
//   //         onChanged: (selected) {
//   //           clearSubKey(subFieldList);
//   //           selectPrintConfig[selected.toString()] = true;
//   //           setState(() {});
//   //         },
//   //       )
//   //       // )
//   //     ],
//   //   ));
//   // }
//
//   // return subCodeList;
//   // }
//
//   ///单选时清除子项选中
//   void clearSubKey(List<String> subFieldList) {
//     for (String fieldKey in subFieldList) {
//       selectPrintConfig[fieldKey] = false;
//     }
//   }
//
//   //endregion
//
//   //region 构建预览
//   ///构建打印表头
//   Widget _buildPrintTitle() {
//     String? billTypeTitle;
//     var billType = billTypeList[selectIndex]["billType"];
//     if (billType == BillTypeData[BillType.SaleBackBill]) {
//       billTypeTitle = "退货单";
//     } else if (billType == BillTypeData[BillType.GoodsTrans]) {
//       billTypeTitle = "调拨单";
//     } else if (billType == BillTypeData[BillType.TransferOrder]) {
//       billTypeTitle = "调拨订单";
//     } else if (billType == BillTypeData[BillType.ChannelBill]) {
//       billTypeTitle = "全渠道订单";
//     }
//     return HaloContainer(
//       direction: Axis.vertical,
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Visibility(
//             visible: selectPrintConfig[PrintFieldConfig.shopName] ?? false,
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 Text(
//                   "xxxx旗舰店",
//                   style: TextStyle(
//                       fontSize: 26.sp,
//                       color: AppColorHelper(context).getTitleBoldTextColor()),
//                 ),
//                 Visibility(
//                     visible: billTypeTitle != null,
//                     child: Text(
//                       "·$billTypeTitle",
//                       style: TextStyle(
//                           fontSize: 26.sp,
//                           color:
//                               AppColorHelper(context).getTitleBoldTextColor()),
//                     ))
//               ],
//             )),
//         _buildRowWidget(
//             name: PrintFieldConfig
//                 .printConfigNameStr[PrintFieldConfig.cashierName],
//             value: LoginCenter.getLoginUser().user,
//             visibleField: PrintFieldConfig.cashierName),
//         _buildRowWidget(
//             name:
//                 PrintFieldConfig.printConfigNameStr[PrintFieldConfig.cashierNo],
//             value: "0001",
//             visibleField: PrintFieldConfig.cashierNo),
//         _buildBillNumber(),
//         _buildRowWidget(
//             name:
//                 PrintFieldConfig.printConfigNameStr[PrintFieldConfig.kFullName],
//             value: "xxx",
//             visibleField: PrintFieldConfig.kFullName),
//         _buildRowWidget(
//             name: PrintFieldConfig
//                 .printConfigNameStr[PrintFieldConfig.kFullName2],
//             value: "xxx",
//             visibleField: PrintFieldConfig.kFullName2),
//         _buildRowWidget(
//             name:
//                 PrintFieldConfig.printConfigNameStr[PrintFieldConfig.eTypeName],
//             value: "xxx",
//             visibleField: PrintFieldConfig.eTypeName),
//         _buildRowWidget(
//             name: PrintFieldConfig
//                 .printConfigNameStr[PrintFieldConfig.createETypeName],
//             value: "xxx",
//             visibleField: PrintFieldConfig.createETypeName),
//         _buildRowWidget(
//             name:
//                 PrintFieldConfig.printConfigNameStr[PrintFieldConfig.billDate],
//             value: "2021-08-21 18:08:09",
//             visibleField: PrintFieldConfig.billDate),
//         _buildRowWidget(
//             name: PrintFieldConfig
//                 .printConfigNameStr[PrintFieldConfig.tenderBillDate],
//             value: "2021-08-21 18:08:09",
//             visibleField: PrintFieldConfig.tenderBillDate),
//         _buildRowWidget(
//             name:
//                 PrintFieldConfig.printConfigNameStr[PrintFieldConfig.billGuide],
//             value: "张三",
//             visibleField: PrintFieldConfig.billGuide),
//         _buildRowWidget(
//             name:
//                 PrintFieldConfig.printConfigNameStr[PrintFieldConfig.billMemo],
//             value: "xxxxx",
//             visibleField: PrintFieldConfig.billMemo),
//         _buildRowWidget(
//             name: PrintFieldConfig.printConfigNameStr[PrintFieldConfig.summary],
//             value: "xxxxx",
//             visibleField: PrintFieldConfig.summary),
//         _buildRowWidget(
//             name: PrintFieldConfig.printConfigNameStr[PrintFieldConfig.comment],
//             value: "xxxx",
//             visibleField: PrintFieldConfig.comment),
//         _buildRowWidget(
//             name: PrintFieldConfig
//                 .printConfigNameStr[PrintFieldConfig.orderNumber],
//             value: "xxxxxxxxxxxxx",
//             visibleField: PrintFieldConfig.orderNumber),
//         _buildRowWidget(
//             name: PrintFieldConfig.printConfigNameStr[PrintFieldConfig.payDate],
//             value: "2021-08-21 18:08:09",
//             visibleField: PrintFieldConfig.payDate),
//         _buildRowWidget(
//             name: PrintFieldConfig
//                 .printConfigNameStr[PrintFieldConfig.buyerMessage],
//             value: "xxxxx",
//             visibleField: PrintFieldConfig.buyerMessage),
//       ],
//     );
//   }
//
//   ///构建单据编号
//   Widget _buildBillNumber() {
//     return HaloContainer(
//       visible: selectPrintConfig[PrintFieldConfig.billNumber] ?? false,
//       mainAxisAlignment: MainAxisAlignment.center,
//       mainAxisSize: MainAxisSize.max,
//       children: [
//         HaloPosLabel(
//             "${PrintFieldConfig.printConfigNameStr[PrintFieldConfig.billNumber]!}:WSD-20210821-001",
//             visible: !(selectPrintConfig[PrintFieldConfig.BILL_NUMBER_QRCODE] ??
//                 false),
//             textStyle: TextStyle(
//                 fontSize: 22.sp,
//                 color: AppColorHelper(context).getTitleBoldTextColor())),
//         Expanded(
//             child: HaloContainer(
//           visible:
//               selectPrintConfig[PrintFieldConfig.BILL_NUMBER_QRCODE] ?? false,
//           direction: Axis.vertical,
//           crossAxisAlignment: CrossAxisAlignment.center,
//           children: [
//             QrImage(
//               data: "WSD-20210821-001",
//               version: QrVersions.auto,
//               size: 100.0,
//             ),
//             HaloPosLabel("WSD-20210821-001",
//                 textStyle: TextStyle(
//                     fontSize: 22.sp,
//                     color: AppColorHelper(context).getTitleBoldTextColor())),
//           ],
//         ))
//       ],
//     );
//   }
//
//   ///构建打印表体
//   Widget _buildPrintContent() {
//     List<Widget> contentWidget = [];
//     contentWidget.add(const DottedLine());
//     contentWidget.add(SizedBox(
//       height: 8.w,
//     ));
//     contentWidget.add(HaloContainer(
//         children: _creatItemWidget(
//       name: "名称",
//       retailPrice:
//           PrintFieldConfig.printConfigNameStr[PrintFieldConfig.retailPrice],
//       price: "现价",
//       qty: "数量",
//       total: "小计",
//       isTitleRow: true,
//     )));
//     contentWidget.add(_buildContentRow(
//         name: "蜂蜜芥末脆皮鸡",
//         retailPrice: "10.0",
//         price: "10.0",
//         qty: "1",
//         total: "10.0",
//         batchNo: "x001",
//         protectDay: "2023-10-12",
//         pTypeUserCode: "2323",
//         skuBarCode: "3344"));
//     contentWidget.add(_buildContentRow(
//         name: "蜂蜜芥末脆皮鸡爽炸鸡腿堡末脆皮鸡爽炸鸡腿堡",
//         retailPrice: "12.0",
//         price: "12.0",
//         qty: "2",
//         total: "24"
//             "",
//         pTypeUserCode: "123"));
//     contentWidget.add(_buildContentRow(
//         name: "中杯可乐",
//         retailPrice: "30",
//         price: "30",
//         qty: "1000",
//         total: ""
//             "30000",
//         pTypeUserCode: "4577",
//         skuBarCode: "5566"));
//     contentWidget.add(SizedBox(
//       height: 16.w,
//     ));
//     contentWidget.add(const DottedLine());
//     return HaloContainer(
//       direction: Axis.vertical,
//       margin: EdgeInsets.symmetric(vertical: 8.w),
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: contentWidget,
//     );
//   }
//
//   ///调拨单商品列表
//   Widget _buildTenderPrintContent() {
//     Map<int, double> columnConfig = {};
//     Map<int, String> columnTitle = {};
//     if (selectPrintConfig[PrintFieldConfig.pFullName] == true) {
//       columnTitle[1] =
//           PrintFieldConfig.printConfigNameStr[PrintFieldConfig.pFullName]!;
//       columnConfig[1] = 1;
//     }
//     if (selectPrintConfig[PrintFieldConfig.userCode] == true) {
//       columnTitle[2] =
//           PrintFieldConfig.printConfigNameStr[PrintFieldConfig.userCode]!;
//       columnConfig[2] = 1;
//     }
//     if (selectPrintConfig[PrintFieldConfig.barcode] == true) {
//       columnTitle[3] =
//           PrintFieldConfig.printConfigNameStr[PrintFieldConfig.barcode]!;
//       columnConfig[3] = 1;
//     }
//     if (selectPrintConfig[PrintFieldConfig.qty] == true) {
//       columnTitle[4] =
//           PrintFieldConfig.printConfigNameStr[PrintFieldConfig.qty]!;
//       columnConfig[4] = 1;
//     }
//     return Column(
//       children: [
//         const DottedLine(),
//         CustomColumnTable<Map<int, String>>(
//           columnConfig: columnConfig,
//           columnTitle: columnTitle,
//           data: const [
//             {
//               1: "蜂蜜芥末脆皮鸡",
//               2: "xxxxxxx",
//               3: "xxxxxxxxxxxxxxxxx",
//               4: "1",
//             },
//             {
//               1: "蜂蜜芥末脆皮鸡",
//               2: "xxxxxxx",
//               3: "xxxxxxxxxxxxxxxxx",
//               4: "1",
//             }
//           ],
//           columnTitleBuilder: (title, columnType) {
//             return Text(title,
//                 textAlign: columnType == 1 ? TextAlign.start : TextAlign.center,
//                 style: TextStyle(
//                     color: AppColorHelper(context).getTitleBoldTextColor(),
//                     fontSize: 22.sp));
//           },
//           cellBuilder: (Map<int, String> item, columnType) {
//             return Text(item[columnType] ?? "",
//                 textAlign: columnType == 1 ? TextAlign.start : TextAlign.center,
//                 style: TextStyle(
//                     color: AppColorHelper(context).getTitleBoldTextColor(),
//                     fontSize: 22.sp));
//           },
//         ),
//       ],
//     );
//   }
//
//   ///全渠道订单
//   Widget _buildChannelPrintContent() {
//     Map<int, double> columnConfig = {};
//     Map<int, String> columnTitle = {};
//     columnTitle[1] = "商品";
//     columnConfig[1] = 2;
//     columnTitle[2] = "现价";
//     columnConfig[2] = 1;
//     columnTitle[3] = "数量";
//     columnConfig[3] = 1;
//     columnTitle[4] = "金额";
//     columnConfig[4] = 1;
//     return Column(
//       children: [
//         const DottedLine(),
//         CustomColumnTable<Map<int, String>>(
//           columnConfig: columnConfig,
//           columnTitle: columnTitle,
//           data: const [
//             {
//               1: "蜂蜜芥末脆皮鸡",
//               2: "10",
//               3: "1",
//               4: "10",
//             },
//             {
//               1: "蜂蜜芥末脆皮鸡",
//               2: "10",
//               3: "1",
//               4: "10",
//             }
//           ],
//           columnTitleBuilder: (title, columnType) {
//             return Text(title,
//                 textAlign: columnType == 1 ? TextAlign.start : TextAlign.center,
//                 style: TextStyle(
//                     color: AppColorHelper(context).getTitleBoldTextColor(),
//                     fontSize: 22.sp));
//           },
//           cellBuilder: (Map<int, String> item, columnType) {
//             return Text(item[columnType] ?? "",
//                 textAlign: columnType == 1 ? TextAlign.start : TextAlign.center,
//                 style: TextStyle(
//                     color: AppColorHelper(context).getTitleBoldTextColor(),
//                     fontSize: 22.sp));
//           },
//         ),
//       ],
//     );
//   }
//
//   ///构建打印表尾
//   Widget _buildPrintFooter() {
//     return HaloContainer(
//       direction: Axis.vertical,
//       margin: EdgeInsets.symmetric(vertical: 8.w),
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         _buildRowWidget(
//             name: PrintFieldConfig.printConfigNameStr[PrintFieldConfig.sumQty],
//             value: "121.05",
//             visibleField: PrintFieldConfig.sumQty),
//         _buildRowWidget(
//             name: PrintFieldConfig
//                 .printConfigNameStr[PrintFieldConfig.tenderSumQty],
//             value: "121.05",
//             visibleField: PrintFieldConfig.tenderSumQty),
//         _buildRowWidget(
//             name:
//                 PrintFieldConfig.printConfigNameStr[PrintFieldConfig.sumTotal],
//             value: "1000.55",
//             visibleField: PrintFieldConfig.sumTotal),
//         _buildRowWidget(
//             name: PrintFieldConfig.printConfigNameStr[PrintFieldConfig.backQty],
//             value: "121.05",
//             visibleField: PrintFieldConfig.backQty),
//         _buildRowWidget(
//             name:
//                 PrintFieldConfig.printConfigNameStr[PrintFieldConfig.backTotal],
//             value: "1000.55",
//             visibleField: PrintFieldConfig.backTotal),
//         _buildRowWidget(
//             name: PrintFieldConfig
//                 .printConfigNameStr[PrintFieldConfig.storeTotal],
//             value: "100",
//             visibleField: PrintFieldConfig.storeTotal),
//         _buildRowWidget(
//             name: PrintFieldConfig.printConfigNameStr[PrintFieldConfig.wTotal],
//             value: "10.12",
//             visibleField: PrintFieldConfig.wTotal),
//         _buildRowWidget(
//             name:
//                 PrintFieldConfig.printConfigNameStr[PrintFieldConfig.pettyCash],
//             value: "3.55",
//             visibleField: PrintFieldConfig.pettyCash),
//         _buildRowWidget(
//             name: PrintFieldConfig.printConfigNameStr[PrintFieldConfig.payment],
//             value: "现金",
//             visibleField: PrintFieldConfig.payment),
//         _buildRowWidget(
//             name: PrintFieldConfig.printConfigNameStr[PrintFieldConfig.vipName],
//             value: "张三",
//             visibleField: PrintFieldConfig.vipName),
//         _buildRowWidget(
//             name:
//                 PrintFieldConfig.printConfigNameStr[PrintFieldConfig.vipPhone],
//             value: "1380000000",
//             visibleField: PrintFieldConfig.vipPhone),
//         _buildRowWidget(
//             name:
//                 PrintFieldConfig.printConfigNameStr[PrintFieldConfig.saleScore],
//             value: "100",
//             visibleField: PrintFieldConfig.saleScore),
//         _buildRowWidget(
//             name: PrintFieldConfig
//                 .printConfigNameStr[PrintFieldConfig.scoreTotal],
//             value: "1000",
//             visibleField: PrintFieldConfig.scoreTotal),
//         _buildRowWidget(
//             name: PrintFieldConfig
//                 .printConfigNameStr[PrintFieldConfig.freightFee],
//             value: "8",
//             visibleField: PrintFieldConfig.freightFee),
//         _buildRowWidget(
//             name: PrintFieldConfig
//                 .printConfigNameStr[PrintFieldConfig.buyerFreight],
//             value: "12",
//             visibleField: PrintFieldConfig.buyerFreight),
//         const DottedLine(),
//         SizedBox(
//           height: 10.w,
//         ),
//         const DottedLine(),
//         SizedBox(
//           height: 10.w,
//         ),
//         _buildRowWidget(
//             name:
//                 PrintFieldConfig.printConfigNameStr[PrintFieldConfig.printDate],
//             value: "2021-08-31 18:20:30",
//             visibleField: PrintFieldConfig.printDate),
//         _buildRowWidget(
//             name: PrintFieldConfig
//                 .printConfigNameStr[PrintFieldConfig.tenderPrintDate],
//             value: "2021-08-31 18:20:30",
//             visibleField: PrintFieldConfig.tenderPrintDate),
//         _buildRowWidget(
//             name: PrintFieldConfig
//                 .printConfigNameStr[PrintFieldConfig.tenderPrintCount],
//             value: "2",
//             visibleField: PrintFieldConfig.tenderPrintCount),
//         _buildRowWidget(
//             name: PrintFieldConfig.printConfigNameStr[PrintFieldConfig.signOut],
//             value: "",
//             visibleField: PrintFieldConfig.signOut),
//         _buildRowWidget(
//             name: PrintFieldConfig.printConfigNameStr[PrintFieldConfig.signIn],
//             value: "",
//             visibleField: PrintFieldConfig.signIn),
//         _buildRowWidget(
//             name: "实打/应打:",
//             value: "3/2",
//             visibleField: PrintFieldConfig.printCount),
//         _buildRowWidget(
//             name: "成都市高新区天软件园件xxx",
//             value: "",
//             visibleField: PrintFieldConfig.address),
//         _buildRowWidget(
//             name: PrintFieldConfig
//                 .printConfigNameStr[PrintFieldConfig.pickupCode],
//             value: "xxxxxx",
//             visibleField: PrintFieldConfig.pickupCode),
//         _buildRowWidget(
//             name: PrintFieldConfig
//                 .printConfigNameStr[PrintFieldConfig.receiverName],
//             value: "张三",
//             visibleField: PrintFieldConfig.receiverName),
//         _buildRowWidget(
//             name: PrintFieldConfig
//                 .printConfigNameStr[PrintFieldConfig.receiverMobile],
//             value: "13888888888",
//             visibleField: PrintFieldConfig.receiverMobile),
//         _buildRowWidget(
//             name: PrintFieldConfig
//                 .printConfigNameStr[PrintFieldConfig.receiverAddress],
//             value: "成都市武侯区xxx小区",
//             visibleField: PrintFieldConfig.receiverAddress),
//         _buildInvoiceCode()
//       ],
//     );
//   }
//
//   ///构建发票打印二维码
//   Widget _buildInvoiceCode() {
//     return Center(
//         child: HaloContainer(
//             visible: selectPrintConfig[PrintFieldConfig.invoiceCode] ?? false,
//             direction: Axis.vertical,
//             crossAxisAlignment: CrossAxisAlignment.center,
//             children: [
//           QrImage(
//             data: buildInvoiceUrl(),
//             version: QrVersions.auto,
//             size: 180.0,
//           ),
//           HaloPosLabel("请微信扫码开发票",
//               textStyle: TextStyle(
//                   fontSize: 22.sp,
//                   color: AppColorHelper(context).getTitleBoldTextColor())),
//         ]));
//   }
//
//   ///生成发票链接
//   String buildInvoiceUrl() {
//     LoginUserModel loginUserModel = LoginCenter.getLoginUser();
//     return "${loginUserModel.requestUrl}/sale/OpenInvoice.html?vchcode=123&profileid=343343&sign=234234234";
//   }
//
//   String getBatchInfo(String? bathNo, String? productDay) {
//     String bathNoStr = "";
//     if ((bathNo?.isNotEmpty ?? false) &&
//         (selectPrintConfig[PrintFieldConfig.pTypeBatchNo] ?? false)) {
//       bathNoStr += "${bathNo ?? ""};";
//     }
//     if ((productDay?.isNotEmpty ?? false) &&
//         (selectPrintConfig[PrintFieldConfig.pTypeProductDay] ?? false)) {
//       bathNoStr += productDay ?? "";
//     }
//     return bathNoStr;
//   }
//
//   Widget _buildContentRow(
//       {String? name,
//       String? retailPrice,
//       String? price,
//       String? qty,
//       String? total,
//       String? batchNo,
//       String? protectDay,
//       String? skuBarCode,
//       String? pTypeUserCode}) {
//     return HaloContainer(
//         direction: Axis.vertical,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         margin: EdgeInsets.only(top: 8.w),
//         children: [
//           HaloContainer(
//             mainAxisSize: MainAxisSize.max,
//             children: [
//               Expanded(
//                   child: Text(
//                 name ?? "",
//                 style: TextStyle(
//                     color: AppColorHelper(context).getTitleBoldTextColor(),
//                     fontSize: 22.sp),
//               ))
//             ],
//           ),
//           Visibility(
//             visible: getBatchInfo(batchNo, protectDay).isNotEmpty,
//             child: Container(
//                 padding: EdgeInsets.only(bottom: 10.w),
//                 child: HaloPosLabel(
//                   "批次:${getBatchInfo(batchNo, protectDay)}",
//                   textStyle: TextStyle(
//                       fontSize: 22.sp,
//                       color: AppColorHelper(context).getTitleBoldTextColor()),
//                 )),
//           ),
//           HaloContainer(
//             mainAxisSize: MainAxisSize.max,
//             children: [
//               Expanded(
//                   child: HaloContainer(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 mainAxisSize: MainAxisSize.max,
//                 children: _creatItemWidget(
//                     name: name,
//                     retailPrice: retailPrice,
//                     price: price,
//                     qty: qty,
//                     total: total,
//                     skuBarCode: skuBarCode,
//                     pTypeUserCode: pTypeUserCode),
//               ))
//             ],
//           )
//         ]);
//   }
//
//   List<Widget> _creatItemWidget(
//       {String? name,
//       String? retailPrice,
//       String? price,
//       String? qty,
//       String? total,
//       String? skuBarCode,
//       String? pTypeUserCode,
//       bool isTitleRow = false}) {
//     List<Widget> contentWidgets = [];
//
//     ///标题行打印名称
//     // if (isTitleRow) {
//     //   contentWidgets.add(buildDetailItem(name, textAlign: TextAlign.left));
//     // } else {
//     //   ///非标题行打印编码
//     //
//     //   ///sku条码是否选中
//     //   bool checkPypeSkuCode =
//     //       selectPrintConfig[PrintFieldConfig.pTypeSkuCode] ?? false;
//     //
//     //   ///商品编号是否选中
//     //   bool checkPypeUserCode =
//     //       selectPrintConfig[PrintFieldConfig.pTypUserCode] ?? false;
//     //
//     //   ///未启用编码配置，使用空白占位
//     //   if (!(selectPrintConfig[PrintFieldConfig.pTypeCode] ?? false)) {
//     //     contentWidgets.add(buildDetailItem(""));
//     //   } else if (checkPypeSkuCode) {
//     //     ///选中sku条码
//     //     contentWidgets
//     //         .add(buildDetailItem(skuBarCode, textAlign: TextAlign.left));
//     //   }
//     //
//     //   ///商品编号选中或者品编码选中且商品编码和sku条码都未选择默认选中商品编号
//     //   else if (checkPypeUserCode || (!checkPypeSkuCode && !checkPypeUserCode)) {
//     //     contentWidgets
//     //         .add(buildDetailItem(pTypeUserCode, textAlign: TextAlign.left));
//     //   }
//     // }
//
//     contentWidgets.add(buildDetailItem(retailPrice, defaultValue: "0"));
//     contentWidgets.add(buildDetailItem(price, defaultValue: "0"));
//     contentWidgets.add(buildDetailItem(qty, defaultValue: "0"));
//     contentWidgets.add(buildDetailItem(total, defaultValue: "0"));
//     return contentWidgets;
//   }
//
//   ///构建明细行的item
//   Widget buildDetailItem(String? value,
//       {String defaultValue = "", int flex = 1, textAlign = TextAlign.center}) {
//     return Expanded(
//         flex: flex,
//         child: Text(
//           value ?? defaultValue,
//           textAlign: textAlign,
//           style: TextStyle(
//               color: AppColorHelper(context).getTitleBoldTextColor(),
//               fontSize: 22.sp),
//         ));
//   }
//
//   Widget _buildRowWidget({
//     String? name,
//     String? value,
//     String? visibleField,
//   }) {
//     return Visibility(
//       visible: null == visibleField
//           ? true
//           : selectPrintConfig[visibleField] ?? false,
//       child: Container(
//           padding: EdgeInsets.only(bottom: 10.w),
//           child: HaloPosLabel(
//             StringUtil.isEmpty(value) ? "$name" : "$name:$value",
//             textStyle: TextStyle(
//                 fontSize: 22.sp,
//                 color: AppColorHelper(context).getTitleBoldTextColor()),
//           )),
//     );
//   }
//
//   //endregion
//
//   //region 构建底部按钮
//   @override
//   Widget buildBottomBody(BuildContext context) {
//     return HaloContainer(
//       mainAxisSize: MainAxisSize.max,
//       height: 110.w,
//       mainAxisAlignment: MainAxisAlignment.center,
//       padding: EdgeInsets.symmetric(vertical: 22.w),
//       borderShadow: const [
//         BoxShadow(
//             offset: Offset(5, 5),
//             color: Color.fromRGBO(206, 206, 206, 0.5),
//             blurRadius: 25)
//       ],
//       color: Colors.white,
//       children: [
//         GestureDetector(
//           onTap: () {
//             SpTool.savePrintField(
//                 billTypeList[selectIndex]["billType"], selectPrintConfig);
//             HaloToast.show(context, msg: "保存成功");
//           },
//           child: Container(
//             height: 66.w,
//             width: 158.w,
//             margin: EdgeInsets.only(left: 20.w),
//             alignment: Alignment.center,
//             decoration: BoxDecoration(
//                 borderRadius: BorderRadius.all(Radius.circular(5.w)),
//                 color: AppColorHelper(context).getAccentColor()),
//             child: Text(
//               "保存",
//               style: TextStyle(fontSize: 26.sp, color: Colors.white),
//             ),
//           ),
//         ),
//         GestureDetector(
//           onTap: () {
//             NavigateUtil.pop(context);
//           },
//           child: Container(
//             height: 66.w,
//             width: 158.w,
//             margin: EdgeInsets.only(left: 20.w),
//             alignment: Alignment.center,
//             decoration: BoxDecoration(
//                 borderRadius: BorderRadius.all(Radius.circular(5.w)),
//                 border: Border.all(color: AppColors.dividerColor, width: 1)),
//             child: Text(
//               "取消",
//               style: TextStyle(
//                   fontSize: 26.sp,
//                   color: AppColorHelper(context).getTitleBoldTextColor()),
//             ),
//           ),
//         )
//       ],
//     );
//   }
//
//   //endregion
//
//   @override
//   Future<void> onInitState() {
//     return Future.value(null);
//   }
//
//   @override
//   String getActionBarTitle() {
//     return "收银小票打印样式";
//   }
// }
